/**
 * Generate command implementation
 */

import { existsSync, statSync } from "fs";
import { join, resolve } from "path";

import chalk from "chalk";
import inquirer from "inquirer";

import type { GenerateCommandOptions, WidgetGenerationOptions } from "../types.js";
import { Logger } from "../utils/logger.js";
import { ProjectAnalyzer } from "../utils/project-analyzer.js";
import { WidgetGenerator } from "../utils/widget-generator.js";

export class GenerateCommand {
  private projectAnalyzer: ProjectAnalyzer;
  private widgetGenerator: WidgetGenerator;

  constructor() {
    this.projectAnalyzer = new ProjectAnalyzer();
    this.widgetGenerator = new WidgetGenerator();
  }

  /**
   * Validate widget name
   */
  private validateWidgetName(name: string): { valid: boolean; error?: string } {
    if (!name) {
      return { valid: false, error: "Widget name is required" };
    }

    if (!/^[A-Z][a-zA-Z0-9]*$/.test(name)) {
      return { valid: false, error: "Widget name must be PascalCase and start with uppercase letter" };
    }

    if (name.length < 3) {
      return { valid: false, error: "Widget name must be at least 3 characters long" };
    }

    return { valid: true };
  }

  /**
   * Validate target path
   */
  private validateTargetPath(targetPath: string): { valid: boolean; error?: string } {
    const resolvedPath = resolve(targetPath);

    if (!existsSync(resolvedPath)) {
      return { valid: false, error: `Target path does not exist: ${resolvedPath}` };
    }

    if (!statSync(resolvedPath).isDirectory()) {
      return { valid: false, error: `Target path is not a directory: ${resolvedPath}` };
    }

    return { valid: true };
  }

  /**
   * Interactive mode for widget generation
   */
  private async runInteractive(): Promise<void> {
    Logger.title("Widget Generator");
    Logger.info("Let's create a new widget component for your CSCS Agent project");

    const answers = await inquirer.prompt([
      {
        type: "input",
        name: "name",
        message: "Widget name (PascalCase):",
        validate: (input: string) => {
          const validation = this.validateWidgetName(input);
          return validation.valid || validation.error || "Invalid widget name";
        },
      },
      {
        type: "list",
        name: "type",
        message: "Widget type:",
        choices: [
          { name: "Button Widget - Interactive button component", value: "button" },
          { name: "Form Widget - Form input component", value: "form" },
          { name: "Select Widget - Dropdown selection component", value: "select" },
          { name: "Display Widget - Data display component", value: "display" },
          { name: "Custom Widget - Custom component with LLM generation", value: "custom" },
        ],
      },
      {
        type: "input",
        name: "targetPath",
        message: "Target project path:",
        default: process.cwd(),
        validate: (input: string) => {
          const validation = this.validateTargetPath(input);
          return validation.valid || validation.error || "Invalid target path";
        },
      },
      {
        type: "list",
        name: "placement",
        message: "Where should this widget be placed?",
        choices: [
          { name: "Message Area - Display in chat messages", value: "message" },
          { name: "Sender Area - Display in input/sender area", value: "sender" },
          { name: "Side Panel - Display in side panel", value: "sidePanel" },
        ],
      },
      {
        type: "list",
        name: "slot",
        message: "Which slot should this widget use?",
        choices: (answers: any) => {
          if (answers.placement === "message") {
            return [
              { name: "Blocks - Main message content", value: "blocks" },
              { name: "Header - Message header slot", value: "header" },
              { name: "Footer - Message footer slot", value: "footer" },
            ];
          } else if (answers.placement === "sender") {
            return [
              { name: "Header Panel - Sender header panel", value: "headerPanel" },
              { name: "Footer - Sender footer slot", value: "footer" },
            ];
          } else {
            return [{ name: "Render - Side panel render area", value: "render" }];
          }
        },
      },
      {
        type: "input",
        name: "description",
        message: "Widget description (optional):",
      },
    ]);

    // Generate widget
    const options: WidgetGenerationOptions = {
      name: answers.name,
      type: answers.type,
      targetPath: answers.targetPath,
      placement: answers.placement,
      slot: answers.slot,
      description: answers.description,
    };

    const success = await this.widgetGenerator.generateWidget(options);

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Non-interactive mode
   */
  private async runNonInteractive(options: GenerateCommandOptions): Promise<void> {
    // Validate required options
    if (!options.name) {
      Logger.error("Widget name is required. Use --name option or run in interactive mode.");
      process.exit(1);
    }

    if (!options.type) {
      Logger.error("Widget type is required. Use --type option or run in interactive mode.");
      process.exit(1);
    }

    // Validate widget name
    const nameValidation = this.validateWidgetName(options.name);
    if (!nameValidation.valid) {
      Logger.error(nameValidation.error || "Invalid widget name");
      process.exit(1);
    }

    // Validate target path
    const targetPath = options.targetPath || process.cwd();
    const pathValidation = this.validateTargetPath(targetPath);
    if (!pathValidation.valid) {
      Logger.error(pathValidation.error || "Invalid target path");
      process.exit(1);
    }

    // Set defaults
    const placement = options.placement || "message";
    const slot =
      options.slot || (placement === "message" ? "blocks" : placement === "sender" ? "headerPanel" : "render");

    // Generate widget
    const generationOptions: WidgetGenerationOptions = {
      name: options.name,
      type: options.type,
      targetPath,
      placement,
      slot,
      description: options.description,
      props: options.props ? JSON.parse(options.props) : undefined,
    };

    const success = await this.widgetGenerator.generateWidget(generationOptions);

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Execute generate command
   */
  async execute(options: GenerateCommandOptions = {}): Promise<void> {
    try {
      if (options.interactive || (!options.name && !options.type)) {
        await this.runInteractive();
      } else {
        await this.runNonInteractive(options);
      }
    } catch (error) {
      Logger.error(`Failed to generate widget: ${error instanceof Error ? error.message : "Unknown error"}`);
      process.exit(1);
    }
  }

  /**
   * List available widget types
   */
  listWidgetTypes(): void {
    Logger.title("Available Widget Types");

    const widgetTypes = [
      { name: "button", description: "Interactive button component with customizable actions" },
      { name: "form", description: "Form input component with validation and submission" },
      { name: "select", description: "Dropdown selection component with static or API data" },
      { name: "display", description: "Data display component for showing information" },
      { name: "custom", description: "Custom component generated using LLM based on specifications" },
    ];

    widgetTypes.forEach((type) => {
      console.log(`  ${chalk.cyan("•")} ${chalk.bold(type.name)} - ${type.description}`);
    });

    Logger.newLine();
  }
}
