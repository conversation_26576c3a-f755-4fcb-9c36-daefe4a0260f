/**
 * Agent configuration update utilities using LLM
 */

import type { WidgetGenerationOptions } from "../types.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { Logger } from "./logger.js";

export class ConfigUpdater {
  private fsManager: FileSystemManager;
  private apiKey: string | undefined;
  private baseUrl: string;
  private model: string;

  constructor() {
    this.fsManager = new FileSystemManager();
    this.apiKey = process.env.OPENAI_API_KEY || "sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy";
    this.baseUrl = process.env.OPENAI_BASE_URL || "http://172.17.9.46:3000/v1";
    this.model = "volcengine/deepseek-v3";
  }

  /**
   * Update agent configuration to include new widget using LLM
   */
  async updateAgentConfig(
    configPath: string,
    options: WidgetGenerationOptions,
    widgetImportPath: string,
    widgetCode: string,
  ): Promise<void> {
    const componentName = options.name;

    if (!this.fsManager.fileExists(configPath)) {
      // Create new agent config file
      const newConfigContent = this.generateNewAgentConfig(options, widgetImportPath, widgetCode);
      await this.fsManager.writeFile(configPath, newConfigContent);
      Logger.info("Created new agent-config.tsx file");
    } else {
      // Update existing agent config file using LLM
      const existingContent = await this.fsManager.readFile(configPath);

      if (!this.apiKey) {
        Logger.warning("OpenAI API key not found. Using fallback manual update.");
        const updatedContent = this.updateExistingAgentConfigManual(existingContent, options, widgetImportPath, widgetCode);
        await this.fsManager.updateFile(configPath, updatedContent, true);
      } else {
        try {
          const updatedContent = await this.updateExistingAgentConfigWithLLM(existingContent, options, widgetImportPath, widgetCode);
          await this.fsManager.updateFile(configPath, updatedContent, true);
          Logger.info("Updated agent-config.tsx file using LLM");
        } catch (error) {
          Logger.warning(`LLM config update failed: ${error instanceof Error ? error.message : "Unknown error"}`);
          Logger.info("Falling back to manual update...");
          const updatedContent = this.updateExistingAgentConfigManual(existingContent, options, widgetImportPath, widgetCode);
          await this.fsManager.updateFile(configPath, updatedContent, true);
        }
      }
    }
  }

  /**
   * Generate new agent config content
   */
  private generateNewAgentConfig(options: WidgetGenerationOptions, importPath: string, widgetCode: string): string {
    const componentName = options.name;

    return `import type { AgentChatConfig } from "@cscs-agent/core";
import ${componentName} from "${importPath}";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Generated Agent",
      code: "generated-agent",
      description: "Agent with generated widgets",
      ${this.generateWidgetPlacement(options, componentName, widgetCode)}
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};
`;
  }

  /**
   * Update existing agent config content using LLM
   */
  private async updateExistingAgentConfigWithLLM(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): Promise<string> {
    const prompt = this.buildConfigUpdatePrompt(content, options, importPath, widgetCode);
    const response = await this.callLLMAPI(prompt);
    return this.extractConfigFromResponse(response);
  }

  /**
   * Update existing agent config content manually (fallback)
   */
  private updateExistingAgentConfigManual(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): string {
    const componentName = options.name;

    // Add import statement
    let updatedContent = this.addImportStatement(content, componentName, importPath);

    // Add widget to appropriate section
    updatedContent = this.addWidgetToConfig(updatedContent, options, componentName, widgetCode);

    return updatedContent;
  }

  /**
   * Add import statement to the config file
   */
  private addImportStatement(content: string, componentName: string, importPath: string): string {
    const importStatement = `import ${componentName} from "${importPath}";`;

    // Find the position to insert the import
    const lines = content.split("\n");
    let insertIndex = 0;

    // Find the last import statement
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().startsWith("import ") && lines[i].includes("from ")) {
        insertIndex = i + 1;
      }
    }

    // Insert the new import
    lines.splice(insertIndex, 0, importStatement);

    return lines.join("\n");
  }

  /**
   * Add widget to the appropriate configuration section
   */
  private addWidgetToConfig(
    content: string,
    options: WidgetGenerationOptions,
    componentName: string,
    widgetCode: string,
  ): string {
    const widgetConfig = `        {
          code: "${widgetCode}",
          component: ${componentName},
        }`;

    // Determine the target section based on placement and slot
    const targetSection = this.getTargetSection(options);

    // Try to find and update the existing section
    const sectionRegex = this.getSectionRegex(targetSection);
    const match = content.match(sectionRegex);

    if (match) {
      // Section exists, add widget to it
      return this.addWidgetToExistingSection(content, widgetConfig, targetSection);
    } else {
      // Section doesn't exist, create it
      return this.createNewSection(content, options, componentName, widgetCode);
    }
  }

  /**
   * Get target section path based on placement and slot
   */
  private getTargetSection(options: WidgetGenerationOptions): string {
    if (options.placement === "message") {
      if (options.slot === "blocks") {
        return "message.blocks.widgets";
      } else {
        return `message.slots.${options.slot}.widgets`;
      }
    } else if (options.placement === "sender") {
      return `sender.slots.${options.slot}.widgets`;
    } else if (options.placement === "sidePanel") {
      return "sidePanel.render.widgets";
    }

    return "message.blocks.widgets"; // default
  }

  /**
   * Get regex pattern for finding a section
   */
  private getSectionRegex(section: string): RegExp {
    const parts = section.split(".");
    let pattern = "";

    for (let i = 0; i < parts.length; i++) {
      if (i === parts.length - 1) {
        // Last part should be 'widgets'
        pattern += `${parts[i]}:\\s*\\[`;
      } else {
        pattern += `${parts[i]}:\\s*\\{[^}]*`;
      }
    }

    return new RegExp(pattern, "s");
  }

  /**
   * Add widget to existing section
   */
  private addWidgetToExistingSection(content: string, widgetConfig: string, section: string): string {
    // Find the widgets array and add the new widget
    const widgetsRegex = /widgets:\s*\[\s*/g;
    let match;
    let lastMatch = null;

    while ((match = widgetsRegex.exec(content)) !== null) {
      lastMatch = match;
    }

    if (lastMatch) {
      const insertPosition = lastMatch.index + lastMatch[0].length;
      return content.slice(0, insertPosition) + widgetConfig + ",\n" + content.slice(insertPosition);
    }

    return content;
  }

  /**
   * Create new section with widget
   */
  private createNewSection(
    content: string,
    options: WidgetGenerationOptions,
    componentName: string,
    widgetCode: string,
  ): string {
    const sectionContent = this.generateWidgetPlacement(options, componentName, widgetCode);

    // Find the agent configuration and add the new section
    const agentRegex = /(\{[^}]*name:\s*["'][^"']*["'][^}]*code:\s*["'][^"']*["'][^}]*)/s;
    const match = content.match(agentRegex);

    if (match && match.index !== undefined) {
      const insertPosition = match.index + match[0].length;
      return content.slice(0, insertPosition) + "\n      " + sectionContent + content.slice(insertPosition);
    }

    return content;
  }

  /**
   * Generate widget placement configuration
   */
  private generateWidgetPlacement(options: WidgetGenerationOptions, componentName: string, widgetCode: string): string {
    const widgetConfig = `{
          code: "${widgetCode}",
          component: ${componentName},
        }`;

    if (options.placement === "message") {
      if (options.slot === "blocks") {
        return `message: {
        blocks: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
      } else {
        return `message: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
      }
    } else if (options.placement === "sender") {
      return `sender: {
        slots: {
          ${options.slot}: {
            widgets: [
              ${widgetConfig}
            ],
          },
        },
      },`;
    } else if (options.placement === "sidePanel") {
      return `sidePanel: {
        render: {
          widgets: [
            ${widgetConfig}
          ],
        },
      },`;
    }

    return "";
  }

  /**
   * Build prompt for LLM to update configuration
   */
  private buildConfigUpdatePrompt(
    content: string,
    options: WidgetGenerationOptions,
    importPath: string,
    widgetCode: string,
  ): string {
    return `You are an expert TypeScript developer working with CSCS Agent configuration files.

**Task**: Update the existing agent configuration file to include a new widget component.

**Current Configuration File Content**:
\`\`\`typescript
${content}
\`\`\`

**Widget Details**:
- Component Name: ${options.name}
- Import Path: ${importPath}
- Widget Code: ${widgetCode}
- Placement: ${options.placement}
- Slot: ${options.slot || "blocks"}
- Description: ${options.description || "A custom widget component"}

**Requirements**:
1. Add the import statement for the new component at the top with other imports
2. Add the widget configuration to the appropriate section based on placement and slot:
   - For placement "message" and slot "blocks": add to message.blocks.widgets array
   - For placement "message" and other slots: add to message.slots.{slot}.widgets array
   - For placement "sender": add to sender.slots.{slot}.widgets array
   - For placement "sidePanel": add to sidePanel.render.widgets array
3. Maintain the existing structure and formatting
4. Preserve all existing imports, configurations, and widgets
5. Follow the existing code style and indentation
6. Ensure the TypeScript syntax is correct

**Widget Configuration Format**:
\`\`\`typescript
{
  code: "${widgetCode}",
  component: ${options.name},
}
\`\`\`

**Important**: Return ONLY the complete updated configuration file content. Do not include any explanations or markdown code blocks.`;
  }

  /**
   * Call LLM API for configuration update
   */
  private async callLLMAPI(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: "system",
            content:
              "You are an expert TypeScript developer specializing in CSCS Agent configuration files. Generate clean, production-ready code that maintains existing structure and follows TypeScript best practices.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 4000,
        temperature: 0.1,
      }),
    });

    if (!response.ok) {
      throw new Error(`LLM API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || "";
  }

  /**
   * Extract configuration content from LLM response
   */
  private extractConfigFromResponse(response: string): string {
    let config = response.trim();

    // Remove markdown code blocks if present
    config = config.replace(/^```(?:typescript|tsx|ts|javascript|jsx|js)?\n/, "");
    config = config.replace(/\n```$/, "");

    // Ensure the config contains required elements
    if (!config.includes("import") || !config.includes("export")) {
      throw new Error("Generated configuration appears to be incomplete");
    }

    return config;
  }
}
